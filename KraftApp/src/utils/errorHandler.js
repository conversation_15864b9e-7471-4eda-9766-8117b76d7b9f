// Fixed errorHandler.js to use alertManager consistently
import logger from './simpleLogger';
import alertManager from './alertManager';

/**
 * Centralized error handling utility
 * @type {Object}
 */
const errorHandler = {
  /**
   * Handle API errors consistently across the app
   *
   * @param {Error} error - The error object
   * @param {Object} options - Error handling options
   * @param {string} options.context - Context where the error occurred (e.g., 'login', 'registration')
   * @param {string} options.fallbackMessage - Default message to show if error doesn't have a message
   * @param {boolean} options.showAlert - Whether to show an alert dialog
   * @param {Function} options.onError - Optional callback to execute after error is handled
   */
  handleApiError: (error, {
    context = 'api_call',
    fallbackMessage = 'Something went wrong. Please try again.',
    showAlert = true,
    onError = null
  } = {}) => {
    // Log the error
    logger.error(`API Error in ${context}`, error);

    // Get readable message
    let errorMessage = fallbackMessage;

    // Extract message from various error formats
    // For fetch errors, we don't have response.data like in axios
    if (error.data?.errors && Array.isArray(error.data.errors)) {
      // Handle validation errors array
      const validationErrors = error.data.errors;
      errorMessage = validationErrors.map(err => `${err.field}: ${err.message}`).join('\n');
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    } else if (error.data?.error) {
      errorMessage = error.data.error;
    } else if (error.message && !error.message.includes('Network Error')) {
      errorMessage = error.message;
    }

    // Special handling for database errors
    if (error.status === 500 && errorMessage.includes('table')) {
      errorMessage = 'Database error: Tables not initialized. Please contact support.';
    }

    // Show alert if needed
    if (showAlert) {
      // Use custom alert manager if available, otherwise fall back to native Alert
      alertManager.showError('Error', errorMessage, [{
        text: 'OK',
        onPress: () => {
          console.log('User acknowledged error in', context);
          // Call the callback if provided
          if (typeof onError === 'function') {
            onError(errorMessage, error);
          }
        }
      }]);
    } else if (typeof onError === 'function') {
      // If no alert but we have a callback, call it
      onError(errorMessage, error);
    }

    return errorMessage;
  },

  /**
   * Handle form validation errors
   *
   * @param {Object} validationResult - Result from form validation
   * @param {boolean} showAlert - Whether to show an alert
   * @returns {boolean} - Returns true if form is valid
   */
  handleValidationError: (validationResult, showAlert = true) => {
    if (validationResult.isValid) {
      return true;
    }

    if (showAlert) {
      alertManager.showWarning('Validation Error', validationResult.message, [{
        text: 'OK',
        onPress: () => {
          console.log('User acknowledged validation error');
        }
      }]);
    }

    return false;
  }
};

export default errorHandler;
