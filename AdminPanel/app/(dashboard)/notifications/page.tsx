"use client"

import { useState, useEffect, useCallback } from "react"
import { Dashboard<PERSON>eader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Bell, Check, CheckCheck, RefreshCw, Trash2, X, Send } from "lucide-react"
import { NotificationService, type Notification } from "@/lib/api/notificationService"
import { toast } from "sonner"
import { useAuth } from "@/lib/context/auth-context"
import { formatDistanceToNow } from "date-fns"

export default function NotificationsPage() {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  })

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    setIsLoading(true)
    try {
      const unreadOnly = activeTab === "unread"
      const response = await NotificationService.getNotifications(
        pagination.page,
        pagination.limit,
        unreadOnly
      )
      setNotifications(response.notifications)
      setPagination(prev => ({
        ...prev,
        total: response.total || 0,
      }))
    } catch (error) {
      console.error("Failed to fetch notifications:", error)
      toast.error("Failed to load notifications")
    } finally {
      setIsLoading(false)
    }
  }, [activeTab, pagination.page, pagination.limit])

  // Initial fetch
  useEffect(() => {
    fetchNotifications()
  }, [fetchNotifications])

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  // Mark notification as read
  const markAsRead = async (id: string) => {
    try {
      await NotificationService.markAsRead(id)
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id ? { ...notification, read: true } : notification
        )
      )
      toast.success("Notification marked as read")
    } catch (error) {
      console.error("Failed to mark notification as read:", error)
      toast.error("Failed to update notification")
    }
  }

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      await NotificationService.markAllAsRead()
      setNotifications(prev => prev.map(notification => ({ ...notification, read: true })))
      toast.success("All notifications marked as read")
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error)
      toast.error("Failed to update notifications")
    }
  }

  // Delete notification
  const deleteNotification = async (id: string) => {
    try {
      await NotificationService.deleteNotification(id)
      setNotifications(prev => prev.filter(notification => notification.id !== id))
      toast.success("Notification deleted")
    } catch (error) {
      console.error("Failed to delete notification:", error)
      toast.error("Failed to delete notification")
    }
  }

  // Get notification type badge color
  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case "order":
        return "bg-blue-100 text-blue-800"
      case "stock":
        return "bg-orange-100 text-orange-800"
      case "inquiry":
        return "bg-green-100 text-green-800"
      case "system":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === "unread") return !notification.read
    return true
  })

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <DashboardShell>
      <DashboardHeader heading="Notifications" text="Manage your notifications and alerts">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={fetchNotifications}>
            <RefreshCw className="h-4 w-4" />
          </Button>
          {unreadCount > 0 && (
            <Button variant="outline" onClick={markAllAsRead}>
              <CheckCheck className="mr-2 h-4 w-4" />
              Mark All Read
            </Button>
          )}
        </div>
      </DashboardHeader>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all" className="relative">
            All Notifications
            {notifications.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {notifications.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread" className="relative">
            Unread
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="send" className="relative">
            Send Promotional
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <NotificationsList
            notifications={filteredNotifications}
            isLoading={isLoading}
            onMarkAsRead={markAsRead}
            onDelete={deleteNotification}
            getTypeBadgeColor={getTypeBadgeColor}
          />
        </TabsContent>

        <TabsContent value="unread" className="space-y-4">
          <NotificationsList
            notifications={filteredNotifications}
            isLoading={isLoading}
            onMarkAsRead={markAsRead}
            onDelete={deleteNotification}
            getTypeBadgeColor={getTypeBadgeColor}
          />
        </TabsContent>

        <TabsContent value="send" className="space-y-4">
          <SendPromotionalNotification />
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}

interface NotificationsListProps {
  notifications: Notification[]
  isLoading: boolean
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
  getTypeBadgeColor: (type: string) => string
}

function NotificationsList({
  notifications,
  isLoading,
  onMarkAsRead,
  onDelete,
  getTypeBadgeColor,
}: NotificationsListProps) {
  if (isLoading) {
    return (
      <div className="flex h-40 w-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    )
  }

  if (notifications.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Bell className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No notifications</h3>
          <p className="text-muted-foreground text-center">
            You're all caught up! No new notifications to display.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {notifications.map((notification) => (
        <Card key={notification.id} className={`${!notification.read ? "border-l-4 border-l-primary" : ""}`}>
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <CardTitle className="text-base">{notification.title}</CardTitle>
                    <Badge className={getTypeBadgeColor(notification.type)}>
                      {notification.type}
                    </Badge>
                    {!notification.read && (
                      <Badge variant="destructive" className="text-xs">
                        New
                      </Badge>
                    )}
                  </div>
                  <CardDescription className="text-sm">
                    {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                {!notification.read && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onMarkAsRead(notification.id)}
                    className="h-8 w-8 p-0"
                  >
                    <Check className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(notification.id)}
                  className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-sm text-muted-foreground">{notification.message}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function SendPromotionalNotification() {
  const [title, setTitle] = useState("")
  const [message, setMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSendNotification = async () => {
    if (!title.trim() || !message.trim()) {
      toast.error("Please fill in both title and message")
      return
    }

    setIsLoading(true)
    try {
      const result = await NotificationService.sendBulkNotification(title.trim(), message.trim())

      if (result.status === 'success') {
        toast.success(`Notification sent to ${result.data.notificationsCreated} users`)
        setTitle("")
        setMessage("")
      } else {
        throw new Error(result.message || 'Failed to send notification')
      }
    } catch (error) {
      console.error('Error sending promotional notification:', error)
      toast.error('Failed to send notification. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Send className="h-5 w-5" />
          Send Promotional Notification
        </CardTitle>
        <CardDescription>
          Send a promotional notification to all approved mobile app users
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="title">Notification Title</Label>
          <Input
            id="title"
            placeholder="Enter notification title..."
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            maxLength={100}
          />
          <p className="text-sm text-muted-foreground">
            {title.length}/100 characters
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="message">Notification Message</Label>
          <Textarea
            id="message"
            placeholder="Enter notification message..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            rows={4}
            maxLength={500}
          />
          <p className="text-sm text-muted-foreground">
            {message.length}/500 characters
          </p>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={handleSendNotification}
            disabled={isLoading || !title.trim() || !message.trim()}
            className="min-w-[120px]"
          >
            {isLoading ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2" />
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Notification
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
