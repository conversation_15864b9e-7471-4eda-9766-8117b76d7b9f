import { Router } from 'express';
import authRoutes from './auth.routes';
import userRoutes from './user.routes';
import stockRoutes from './stock.routes';
import cartRoutes from './cart.routes';
import orderRoutes from './order.routes';
import enquiryRoutes from './enquiry.routes';
import notificationRoutes from './notification.routes';
import adminRoutes from './admin.routes';
import healthRoutes from './health.routes';
import paymentTermUpgradeRoutes from './paymentTermUpgrade.routes';
import supportQueryRoutes from './supportQuery.routes';

const router = Router();

// Health check route
router.use('/health', healthRoutes);

// API routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/stock', stockRoutes);
router.use('/cart', cartRoutes);
router.use('/orders', orderRoutes);
router.use('/enquiries', enquiryRoutes);
router.use('/notifications', notificationRoutes);
router.use('/admin', adminRoutes);
router.use('/payment-term-upgrades', paymentTermUpgradeRoutes);
router.use('/support-queries', supportQueryRoutes);

export default router;
