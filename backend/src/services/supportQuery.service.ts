import prisma from '../config/database';
import { AppError } from '../utils/errorHandler';
import { SupportQuery, QueryStatus } from '@prisma/client';
import { createNotification } from './notification.service';

/**
 * Get user's support queries
 * @param userId - User ID
 * @param options - Query options for filtering and pagination
 * @returns Paginated support queries
 */
export const getUserSupportQueries = async (
  userId: string,
  options: {
    page?: number;
    limit?: number;
    status?: QueryStatus;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }
) => {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = { userId };
  if (status) where.status = status;

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get queries with pagination
  const [queries, total] = await Promise.all([
    prisma.supportQuery.findMany({
      where,
      orderBy,
      skip,
      take: limit,
    }),
    prisma.supportQuery.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    queries,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Get all support queries (admin only)
 * @param options - Query options for filtering and pagination
 * @returns Paginated support queries with user details
 */
export const getAllSupportQueries = async (options: {
  page?: number;
  limit?: number;
  status?: QueryStatus;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) => {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  if (status) where.status = status;

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get queries with pagination
  const [queries, total] = await Promise.all([
    prisma.supportQuery.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        rider: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true,
            email: true,
            contactNumber: true,
          },
        },
      },
    }),
    prisma.supportQuery.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    queries,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Get support query by ID
 * @param queryId - Query ID
 * @param userId - User ID (optional, for authorization)
 * @returns Support query
 */
export const getSupportQueryById = async (
  queryId: string,
  userId?: string
): Promise<SupportQuery> => {
  const where: any = { id: queryId };
  if (userId) where.userId = userId;

  const query = await prisma.supportQuery.findFirst({
    where,
    include: {
      rider: {
        select: {
          id: true,
          companyName: true,
          contactPerson: true,
          email: true,
          contactNumber: true,
        },
      },
    },
  });

  if (!query) {
    throw new AppError('Support query not found', 404);
  }

  return query;
};

/**
 * Create new support query
 * @param userId - User ID
 * @param data - Query data
 * @returns Created support query
 */
export const createSupportQuery = async (
  userId: string,
  data: {
    query: string;
    email?: string;
    phone?: string;
    name?: string;
  }
): Promise<SupportQuery> => {
  // Create query
  const supportQuery = await prisma.supportQuery.create({
    data: {
      userId,
      query: data.query,
      email: data.email,
      phone: data.phone,
      name: data.name,
    },
  });

  // Create notification for user
  await createNotification(
    userId,
    'ENQUIRY_RESPONSE',
    'Support Query Submitted',
    `Your support query has been submitted and will be responded to shortly.`
  );

  return supportQuery;
};

/**
 * Respond to support query (admin only)
 * @param queryId - Query ID
 * @param response - Response text
 * @returns Updated support query
 */
export const respondToSupportQuery = async (
  queryId: string,
  response: string
): Promise<SupportQuery> => {
  // Get query
  const query = await getSupportQueryById(queryId);

  // Check if query is still pending
  if (query.status !== QueryStatus.PENDING) {
    throw new AppError('Only pending queries can be responded to', 400);
  }

  // Update query with response
  const updatedQuery = await prisma.supportQuery.update({
    where: { id: queryId },
    data: {
      response,
      status: QueryStatus.RESPONDED,
      respondedAt: new Date(),
    },
  });

  // Create notification for user
  await createNotification(
    query.userId,
    'ENQUIRY_RESPONSE',
    'Support Query Response',
    `Your support query has been responded to. Please check your queries for the response.`
  );

  return updatedQuery;
};

/**
 * Update support query status
 * @param queryId - Query ID
 * @param userId - User ID (for authorization)
 * @param status - New status
 * @returns Updated support query
 */
export const updateSupportQueryStatus = async (
  queryId: string,
  userId: string,
  status: QueryStatus
): Promise<SupportQuery> => {
  // Get query and check ownership
  const query = await getSupportQueryById(queryId, userId);

  // Only allow closing queries
  if (status !== QueryStatus.CLOSED) {
    throw new AppError('Only CLOSED status is allowed for user updates', 400);
  }

  // Update query status
  const updatedQuery = await prisma.supportQuery.update({
    where: { id: queryId },
    data: { status },
  });

  return updatedQuery;
};
