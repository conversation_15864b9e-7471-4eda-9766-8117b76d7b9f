// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  output        = "../generated/prisma"
  binaryTargets = ["native"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Model
model Rider {
  id              String         @id @default(uuid())
  gstNumber       String         @unique
  companyName     String
  contactPerson   String
  contactNumber   String
  email           String         @unique
  password        String?        // Hashed password (nullable for Firebase users)
  firebaseUid     String?        // Firebase User ID for Firebase Authentication
  fcmToken        String?        // FCM token for push notifications
  paymentTerms    PaymentTerms
  isApproved      Boolean        @default(false)
  approvedAt      DateTime?
  rejectedAt      DateTime?
  rejectionReason String?
  resetToken      String?        // For password reset
  resetTokenExpiry DateTime?     // Expiry time for reset token
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  cart          CartItem[]
  orders        Order[]
  enquiries     Enquiry[]
  notifications Notification[]
  paymentTermUpgradeRequests PaymentTermUpgradeRequest[]
  SupportQueries SupportQuery[]

  @@index([email, contactNumber])
}

enum PaymentTerms {
  IMMEDIATE
  THIRTY_DAYS
  SIXTY_DAYS
}

// Stock Model
model Stock {
  id             String      @id @default(uuid())
  type           String      // e.g., NS, GY
  gsm            Int         // e.g., 80, 120
  bf             Int         // Bursting Factor, e.g., 14, 18
  rollsAvailable Int
  width          Int
  immediatePrice Float       // Price for immediate payment
  thirtyDayPrice Float       // Price for 30-day payment terms
  sixtyDayPrice  Float       // Price for 60-day payment terms
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  cartItems      CartItem[]
  orderItems     OrderItem[]

  @@unique([type, gsm, bf,width])
}

// Cart Model
model CartItem {
  id        String   @id @default(uuid())
  userId    String
  stockId   String
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  rider      Rider     @relation(fields: [userId], references: [id])
  stock     Stock    @relation(fields: [stockId], references: [id])

  @@unique([userId, stockId])
}

// Order Models
model Order {
  id             String        @id @default(uuid())
  orderNumber    String        @unique
  userId         String
  subtotalAmount Float         // Amount before GST
  gstAmount      Float         // GST amount (12%)
  totalAmount    Float         // Final amount including GST
  status         OrderStatus   @default(PENDING)
  paymentTerms   PaymentTerms
  paymentStatus  PaymentStatus @default(PENDING)
  paymentDueDate DateTime?
  fulfillmentType FulfillmentType @default(STANDARD_SHIPPING)
  shippingAddress Json?         // Optional for self-pickup orders
  vehicleNumber  String?        // Required for self-pickup orders
  notes          String?
  createdAt      DateTime      @default(now())
  approvedAt     DateTime?
  shippedAt      DateTime?
  deliveredAt    DateTime?
  cancelledAt    DateTime?
  updatedAt      DateTime      @updatedAt

  // Relations
  rider           Rider          @relation(fields: [userId], references: [id])
  items          OrderItem[]
}

model OrderItem {
  id           String  @id @default(uuid())
  orderId      String
  stockId      String
  type         String  // Denormalized from Stock
  gsm          Int     // Denormalized from Stock
  bf           Int     // Denormalized from Stock
  width        Int     // Denormalized from Stock
  quantity     Int
  pricePerRoll Float
  totalPrice   Float

  // Relations
  order        Order   @relation(fields: [orderId], references: [id])
  stock        Stock   @relation(fields: [stockId], references: [id])

  @@unique([orderId, stockId])
}

enum OrderStatus {
  PENDING
  APPROVED
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  PAID
  OVERDUE
}

enum FulfillmentType {
  STANDARD_SHIPPING
  SELF_PICKUP
}

// Enquiry Models
model Enquiry {
  id                String           @id @default(uuid())
  enquiryNumber     String           @unique
  userId            String
  type              String
  gsm               String
  bf                String 
  width             String
  quantity          Int
  message           String?
  status            EnquiryStatus    @default(PENDING)
  contactPreference ContactPreference
  response          String?
  createdAt         DateTime         @default(now())
  respondedAt       DateTime?
  updatedAt         DateTime         @updatedAt

  // Relations
  rider              Rider             @relation(fields: [userId], references: [id])
}

enum EnquiryStatus {
  PENDING
  RESPONDED
  CLOSED
}

enum ContactPreference {
  EMAIL
  PHONE
}

// Notification Model
model Notification {
  id        String            @id @default(uuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  isRead    Boolean           @default(false)
  createdAt DateTime          @default(now())
  readAt    DateTime?

  // Relations
  rider      Rider              @relation(fields: [userId], references: [id])
}

enum NotificationType {
  ORDER_STATUS
  ENQUIRY_RESPONSE
  ACCOUNT_APPROVAL
  STOCK_UPDATE
  PAYMENT_REMINDER
  PROMOTION
}

// OTP Model for Email Verification
model OTP {
  id        String   @id @default(uuid())
  email     String   @unique
  code      String
  expiresAt DateTime
  createdAt DateTime @default(now())
}

model PaymentTermUpgradeRequest {
  id        String   @id @default(uuid())
  userId    String
  reason    String
  currentPaymentTerms PaymentTerms
  requestedPaymentTerms PaymentTerms
  status    RequestStatus @default(PENDING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  rider      Rider              @relation(fields: [userId], references: [id])
}

enum RequestStatus {
  PENDING
  APPROVED
  REJECTED
}

model SupportQuery{
  id        String   @id @default(uuid())
  userId    String
  query     String
  email     String?
  phone     String?
  name      String?
  status    QueryStatus @default(PENDING)
  response  String?
  createdAt DateTime @default(now())
  respondedAt DateTime?

  // Relations
  rider      Rider              @relation(fields: [userId], references: [id])
}

enum QueryStatus {
  PENDING
  RESPONDED
  CLOSED
}